package logic

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"git.keepfancy.xyz/back-end/frameworks/lib/logdog"
	"github.com/sirupsen/logrus"
	"github.com/spf13/cast"
	"io/ioutil"
	"log"
	"net/http"
	"strings"
)

const fsUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/6f328c2e-f648-4534-80d7-6ea882542e42"

type AlertLogic struct {
}

func NewAlertLogic() *AlertLogic {
	return &AlertLogic{}
}

func (l *AlertLogic) HandleMessage(ctx context.Context, evt event.Event) error {
	// 直接解析为 logdog.LogMessage 结构
	var msg logdog.LogMessage
	if err := json.Unmarshal(evt.Value(), &msg); err != nil {
		log.Printf("Failed to unmarshal as LogMessage, trying fallback: %v", err)
		return l.handleFallbackMessage(evt.Value())
	}

	// 提取字段信息
	timestamp := getDefaultValue(msg.Timestamp, "未知时间")
	level := getDefaultValue(msg.Level, "unknown")
	message := getDefaultValue(msg.Message, "无消息内容")
	caller := getDefaultValue(msg.Caller, "未知位置")
	module := getDefaultValue(msg.Source, "unknown")

	var funcName string
	if msg.Fields != nil {
		funcName = getDefaultValue(cast.ToString(msg.Fields["caller_function"]), "未知函数")
	}

	// 安全处理消息内容
	safeMessage := sanitizeForJSON(message)
	details := sanitizeForJSON(string(evt.Value()))

	// 构建推送消息
	pushMsg := fmt.Sprintf(messageTemplate, timestamp, caller, funcName, safeMessage, details, module+"  "+level)

	// 异步发送
	go l.sendFeishuMsg(pushMsg, fsUrl)

	return nil
}

// 降级处理：尝试解析为通用map格式
func (l *AlertLogic) handleFallbackMessage(rawData []byte) error {
	var msgData map[string]interface{}
	if err := json.Unmarshal(rawData, &msgData); err != nil {
		return fmt.Errorf("failed to unmarshal fallback message: %w", err)
	}

	timestamp := cast.ToString(msgData["timestamp"])
	if timestamp == "" {
		timestamp = "未知时间"
	}

	level := cast.ToString(msgData["level"])
	if level == "" {
		level = "unknown"
	}

	message := cast.ToString(msgData["message"])
	if message == "" {
		message = cast.ToString(msgData["msg"])
	}
	if message == "" {
		message = "无消息内容"
	}

	caller := cast.ToString(msgData["caller"])
	if caller == "" {
		file := cast.ToString(msgData["file"])
		line := cast.ToInt(msgData["line"])
		if file != "" && line > 0 {
			caller = fmt.Sprintf("%s:%d", file, line)
		} else if file != "" {
			caller = file
		} else {
			caller = "未知位置"
		}
	}

	module := cast.ToString(msgData["module"])
	if module == "" {
		module = cast.ToString(msgData["source"])
	}
	if module == "" {
		module = "unknown"
	}

	safeMessage := sanitizeForJSON(message)
	details := sanitizeForJSON(string(rawData))

	pushMsg := fmt.Sprintf(messageTemplate, timestamp, caller, "未知函数", safeMessage, details, module+"  "+level)

	go l.sendFeishuMsg(pushMsg, fsUrl)

	return nil
}

// sendFeishuMsg 发送飞书消息
func (l *AlertLogic) sendFeishuMsg(pushMsg string, url string) {
	if url == "" {
		logrus.Infof("AlertLogic sendFeishuMsg get feishu url empty")
		return
	}

	// 记录发送的消息内容用于调试
	logrus.Debugf("AlertLogic.sendFeishuMsg sending message: %s", pushMsg)

	resp, err := http.Post(url, "application/json", bytes.NewBuffer([]byte(pushMsg)))
	if err != nil {
		logrus.Errorf("AlertLogic.sendFeishuMsg http.Post failed, url:%s, err:%s, pushMsg:%s", url, err.Error(), pushMsg)
		return
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logrus.Errorf("AlertLogic.sendFeishuMsg read response body failed, url:%s, err:%s, pushMsg:%s", url, err.Error(), pushMsg)
		return
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		logrus.Errorf("AlertLogic.sendFeishuMsg HTTP error, url:%s, status:%d, resp:%s, pushMsg:%s", url, resp.StatusCode, string(body), pushMsg)
		return
	}

	logrus.Infof("AlertLogic.sendFeishuMsg success, url:%s, resp:%s", url, string(body))
}

// 获取默认值
func getDefaultValue(value, defaultValue string) string {
	if value == "" {
		return defaultValue
	}
	return value
}

// sanitizeForJSON 清理字符串以避免JSON格式问题
func sanitizeForJSON(s string) string {
	if s == "" {
		return "无内容"
	}

	// 移除可能导致JSON问题的字符
	s = strings.ReplaceAll(s, "\"", "'")
	s = strings.ReplaceAll(s, "\n", " ")
	s = strings.ReplaceAll(s, "\r", " ")
	s = strings.ReplaceAll(s, "\t", " ")

	// 限制长度以避免消息过长
	if len(s) > 1000 {
		s = s[:1000] + "..."
	}

	return s
}

var messageTemplate = `{
    "msg_type": "interactive",
    "card": {
        "config": {
            "wide_screen_mode": true,
            "enable_forward": true
        },
        "elements": [
            {
                "tag": "div",
                "text": {
                    "content": "**时间:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**文件:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**函数:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**内容:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "div",
                "text": {
                    "content": "**详情:** %s",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "hr"
            }
        ],
        "header": {
            "template": "red",
            "title": {
                "tag": "plain_text",
                "content": "%s"
            }
        }
    }
}`
