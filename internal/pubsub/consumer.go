package pubsub

import (
	"alertsrv/internal/logic"
	"context"
	"fmt"

	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
)

type Consumer struct {
	alertLogic *logic.AlertLogic
}

func NewConsumer() *Consumer {
	return &Consumer{
		alertLogic: logic.NewAlertLogic(),
	}
}

func (c *Consumer) Consume(ctx context.Context) error {
	receiver, err := event.NewKafkaReceiver(event.WithTopic("logdog_logs"))
	if err != nil {
		return fmt.Errorf("failed to create kafka receiver: %w", err)
	}
	defer receiver.Close()

	if err = receiver.SafeReceive(ctx, c.handleMessage); err != nil {
		return fmt.Errorf("failed to receive from kafka: %w", err)
	}

	return nil
}

func (c *Consumer) handleMessage(ctx context.Context, evt event.Event) error {
	// 调用 logic 层处理消息
	return c.alertLogic.HandleMessage(ctx, evt)
}
