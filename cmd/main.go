package main

import (
	"alertsrv/internal/proc"
	"context"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
	"runtime"
)

type alertService struct {
	Name string
	As   *proc.AlertService
	Ctx  context.Context
}

func (l *alertService) Init() error {
	l.Ctx = context.Background()
	l.Name = viper.GetString(dict.ConfigRpcServerName)
	l.As = proc.NewAlertService()
	logrus.Infoln(l.Name + "服务Init")

	return nil
}

func (l *alertService) Start() error {
	logrus.Infoln(l.Name + "服务启动中...")
	return l.As.Start(l.Ctx)
}

func (l *alertService) Stop() error {
	logrus.Errorf(l.Name + "服务关闭中...")

	return nil
}

func (l *alertService) ForceStop() error {
	logrus.Infoln(l.Name + " ForceStop ...")
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource()

	driver.Run(&alertService{})
}
